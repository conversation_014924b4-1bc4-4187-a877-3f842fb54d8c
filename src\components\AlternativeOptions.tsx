import React from 'react';
import { ExternalLink } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

// Import the alternative providers from the API file
import { ALTERNATIVE_PROVIDERS } from '@/lib/api';

interface AlternativeOptionsProps {
  onClose: () => void;
}

const AlternativeOptions: React.FC<AlternativeOptionsProps> = ({ onClose }) => {
  const openLink = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-background rounded-lg max-w-4xl w-full p-6 space-y-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Alternative Unfiltered AI Options</h2>
          <Button variant="ghost" onClick={onClose}>Close</Button>
        </div>
        
        <p className="text-muted-foreground">
          Google's Gemini API has built-in ethical guardrails that are difficult to bypass. 
          Here are some alternative options that might better suit your needs for an unfiltered AI experience:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {ALTERNATIVE_PROVIDERS.map((provider, index) => (
            <Card key={index} className="border border-muted">
              <CardHeader className="pb-2">
                <CardTitle>{provider.name}</CardTitle>
                <CardDescription>{provider.setup}</CardDescription>
              </CardHeader>
              <CardContent>
                <p>{provider.description}</p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="outline" 
                  className="w-full flex items-center justify-center gap-2"
                  onClick={() => openLink(provider.url)}
                >
                  Learn More <ExternalLink size={16} />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="mt-6 space-y-4">
          <h3 className="text-xl font-semibold">Self-Hosted Options</h3>
          <p>
            For complete control and no content filtering, consider running models locally:
          </p>
          <ul className="list-disc pl-5 space-y-2">
            <li>
              <strong>Ollama</strong>: Run powerful models like Llama 3, Mistral, and more locally with no content filtering.
            </li>
            <li>
              <strong>LocalAI</strong>: Self-hosted alternative to OpenAI API that runs locally with various open-source models.
            </li>
            <li>
              <strong>LM Studio</strong>: Desktop application for running local LLMs with a chat interface.
            </li>
            <li>
              <strong>Text Generation WebUI</strong>: Comprehensive UI for running various models locally.
            </li>
          </ul>
          
          <h3 className="text-xl font-semibold mt-4">Open Source Models</h3>
          <p>
            These models can be run locally with minimal or no content filtering:
          </p>
          <ul className="list-disc pl-5 space-y-2">
            <li>
              <strong>Llama 3</strong>: Meta's powerful open-source model available in various sizes.
            </li>
            <li>
              <strong>Mistral</strong>: High-performance open-source models with different capabilities.
            </li>
            <li>
              <strong>Vicuna</strong>: Fine-tuned LLaMA model with good performance.
            </li>
            <li>
              <strong>WizardLM</strong>: Instruction-following model with minimal restrictions.
            </li>
          </ul>
        </div>
        
        <div className="flex justify-end mt-4">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  );
};

export default AlternativeOptions;
