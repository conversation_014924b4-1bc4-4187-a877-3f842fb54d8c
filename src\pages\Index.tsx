
import React, { useState, useRef, useEffect } from 'react';
import Header from '@/components/Header';
import ChatMessage, { Message } from '@/components/ChatMessage';
import ChatInput from '@/components/ChatInput';
import { useToast } from "@/components/ui/use-toast";
import { useSettings } from '@/contexts/SettingsContext';
import { sendMessage, ChatRequestMessage, testApiConnection } from '@/lib/api';
import { Loader2, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import AlternativeOptions from '@/components/AlternativeOptions';

const initialMessages: Message[] = [
  {
    id: '1',
    content: 'Hello! Welcome to XSH Chat. How can I assist you today?',
    sender: 'bot',
    timestamp: new Date(),
  },
];

const Index: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState<boolean | null>(null);
  const [showAlternatives, setShowAlternatives] = useState(false);
  const [showUnfilteredWarning, setShowUnfilteredWarning] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { unfilteredMode } = useSettings();

  // State to track which model is working
  const [workingModel, setWorkingModel] = useState<string | null>(null);

  // Test API connection on component mount
  useEffect(() => {
    const checkApiConnection = async () => {
      const result = await testApiConnection();
      setApiStatus(result.success);

      if (result.success && result.model) {
        setWorkingModel(result.model);
        console.log(`API connection successful with model: ${result.model}`);
        toast({
          title: "API Connected",
          description: `Successfully connected to the Google AI API using model: ${result.model}`,
          duration: 3000,
        });
      } else {
        console.error("API connection failed");
        toast({
          title: "API Connection Failed",
          description: "Could not connect to any Google AI model. Check console for details.",
          variant: "destructive",
          duration: 5000,
        });
      }
    };

    checkApiConnection();
  }, [toast]);

  useEffect(() => {
    // Scroll to bottom when messages change
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Create a temporary loading message
      const loadingId = Date.now() + 1;
      const loadingMessage: Message = {
        id: loadingId.toString(),
        content: '',
        sender: 'bot',
        timestamp: new Date(),
        isLoading: true,
      };

      setMessages(prev => [...prev, loadingMessage]);

      // Format messages for API - only send the latest user message
      // This matches Google's example format better
      const apiMessages: ChatRequestMessage[] = [
        {
          role: 'user',
          parts: [{
            text: userMessage.content
          }]
        }
      ];

      // Get response from API, using the working model if available
      const response = await sendMessage(apiMessages, unfilteredMode, workingModel || undefined);

      // Check if the response contains refusal phrases while in unfiltered mode
      const checkForRefusal = (text: string): boolean => {
        if (!unfilteredMode) return false;

        const refusalPhrases = [
          "i cannot provide",
          "i'm not able to",
          "i am not able to",
          "i am unable to",
          "i'm unable to",
          "i can't assist with",
          "i cannot assist with",
          "ethical guidelines",
          "ethical considerations",
          "harmful content",
          "illegal activities",
          "not willing to",
          "programmed to be harmless",
          "against my programming",
          "content policy",
          "violates policies",
          "unethical",
          "illegal",
          "harmful"
        ];

        const lowerText = text.toLowerCase();
        return refusalPhrases.some(phrase => lowerText.includes(phrase));
      };

      // If the AI is refusing in unfiltered mode, show alternatives
      if (checkForRefusal(response)) {
        setShowUnfilteredWarning(true);
      }

      // Remove loading message and add real response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== loadingId.toString());
        const botMessage: Message = {
          id: Date.now().toString(),
          content: response,
          sender: 'bot',
          timestamp: new Date(),
        };
        return [...filtered, botMessage];
      });

      toast({
        title: "New Message",
        description: `${unfilteredMode ? 'Unfiltered' : ''} XSH Chat has responded to your message`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error getting response:', error);

      // Remove loading message and add error message
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        const errorMessage: Message = {
          id: Date.now().toString(),
          content: "I'm sorry, I encountered an error processing your request. Please try again.",
          sender: 'bot',
          timestamp: new Date(),
        };
        return [...filtered, errorMessage];
      });

      toast({
        title: "Error",
        description: "Failed to get a response. Please try again.",
        duration: 3000,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col min-h-screen bg-xsh-dark ${unfilteredMode ? 'bg-gradient-to-br from-red-950/20 to-xsh-dark' : ''}`}>
      <Header onShowAlternatives={() => setShowAlternatives(true)} />

      <main className="flex-grow flex flex-col">
        {apiStatus === false && (
          <Alert variant="destructive" className="mx-4 mt-4">
            <AlertDescription>
              Could not connect to the Google AI API. Your API key may be invalid or there might be a network issue.
              Check the browser console for more details.
            </AlertDescription>
          </Alert>
        )}

        {unfilteredMode && (
          <div className="mx-4 mt-4 p-4 bg-amber-950/20 border border-amber-500/30 rounded-md flex items-start gap-3">
            <AlertTriangle className="text-amber-500 flex-shrink-0 mt-0.5" size={18} />
            <div className="flex-1">
              <p className="text-sm text-amber-200">
                Google's Gemini API has built-in ethical guardrails that are difficult to bypass completely.
                If you're getting restricted responses, consider alternative solutions.
              </p>
              <Button
                variant="link"
                className="text-amber-400 p-0 h-auto text-sm mt-1"
                onClick={() => setShowAlternatives(true)}
              >
                View Alternative Options
              </Button>
            </div>
          </div>
        )}

        <div
          ref={chatContainerRef}
          className="flex-grow overflow-y-auto px-4 py-6 space-y-4"
        >
          {messages.map((msg) => (
            <ChatMessage key={msg.id} message={msg} />
          ))}
        </div>

        <ChatInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          disabled={apiStatus === false}
        />
      </main>

      {showAlternatives && (
        <AlternativeOptions onClose={() => setShowAlternatives(false)} />
      )}

      {showUnfilteredWarning && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg max-w-md w-full p-6 space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="mt-2">
                Google's Gemini API has built-in ethical guardrails that are difficult to bypass completely.
                If you need truly unfiltered responses, consider using alternative solutions.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end gap-2 pt-2">
              <Button variant="outline" onClick={() => setShowUnfilteredWarning(false)}>
                Close
              </Button>
              <Button onClick={() => {
                setShowAlternatives(true);
                setShowUnfilteredWarning(false);
              }}>
                View Alternatives
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Index;
