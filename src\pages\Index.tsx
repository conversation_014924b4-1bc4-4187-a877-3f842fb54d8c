
import React, { useState, useRef, useEffect } from 'react';
import Header from '@/components/Header';
import ChatMessage, { Message } from '@/components/ChatMessage';
import ChatInput from '@/components/ChatInput';
import { useToast } from "@/components/ui/use-toast";
import { useSettings } from '@/contexts/SettingsContext';
import { sendMessage, ChatRequestMessage, testApiConnection } from '@/lib/api';
import { Loader2, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import AlternativeOptions from '@/components/AlternativeOptions';

const initialMessages: Message[] = [
  {
    id: '1',
    content: 'Welcome To XSH Chat. I was created by XSH to serve you. What\'s Up?',
    sender: 'bot',
    timestamp: new Date(),
  },
];

const Index: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState<boolean | null>(null);
  const [showAlternatives, setShowAlternatives] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { unfilteredMode } = useSettings();

  // State to track which model is working
  const [workingModel, setWorkingModel] = useState<string | null>(null);

  // Test API connection on component mount
  useEffect(() => {
    const checkApiConnection = async () => {
      const result = await testApiConnection();
      setApiStatus(result.success);

      if (result.success && result.model) {
        setWorkingModel(result.model);
        console.log(`API connection successful with model: ${result.model}`);
        toast({
          title: "API Connected",
          description: `Connected successfully using model: XSH_Dev beta 1.1`,
          duration: 3000,
        });
      } else {
        console.error("API connection failed");
        toast({
          title: "API Connection Failed",
          description: "Could not connect to API. Check console for details.",
          variant: "destructive",
          duration: 5000,
        });
      }
    };

    checkApiConnection();
  }, [toast]);

  useEffect(() => {
    // Scroll to bottom when messages change
    const scrollToBottom = () => {
      if (chatContainerRef.current) {
        chatContainerRef.current.scrollTo({
          top: chatContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    };

    // Multiple timeouts to ensure scrolling works
    setTimeout(scrollToBottom, 50);
    setTimeout(scrollToBottom, 200);
    setTimeout(scrollToBottom, 500);
  }, [messages]);

  const handleNewChat = () => {
    setMessages(initialMessages);
    setIsLoading(false);
  };

  const handleSendMessage = async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Create a temporary loading message
      const loadingId = Date.now() + 1;
      const loadingMessage: Message = {
        id: loadingId.toString(),
        content: '',
        sender: 'bot',
        timestamp: new Date(),
        isLoading: true,
      };

      setMessages(prev => [...prev, loadingMessage]);

      // Format messages for Venice.ai API (OpenAI format)
      const apiMessages: ChatRequestMessage[] = [
        {
          role: 'user',
          content: userMessage.content
        }
      ];

      // Get response from API, using the working model if available
      const response = await sendMessage(apiMessages, unfilteredMode, workingModel || undefined);

      // Remove loading message and add real response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== loadingId.toString());
        const botMessage: Message = {
          id: Date.now().toString(),
          content: response,
          sender: 'bot',
          timestamp: new Date(),
        };
        return [...filtered, botMessage];
      });


    } catch (error) {
      console.error('Error getting response:', error);

      // Remove loading message and add error message
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        const errorMessage: Message = {
          id: Date.now().toString(),
          content: "I'm sorry, I encountered an error processing your request. Please try again.",
          sender: 'bot',
          timestamp: new Date(),
        };
        return [...filtered, errorMessage];
      });

      toast({
        title: "Error",
        description: "Failed to get a response. Please try again.",
        duration: 3000,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col min-h-screen bg-xsh-dark ${unfilteredMode ? 'bg-gradient-to-br from-red-950/20 to-xsh-dark' : ''}`}>
      <Header onShowAlternatives={() => setShowAlternatives(true)} onNewChat={handleNewChat} />

      <main className="flex-grow flex flex-col">
        {apiStatus === false && (
          <Alert variant="destructive" className="mx-4 mt-4">
            <AlertDescription>
              Could not connect to API. Your API key may be invalid or there might be a network issue.
              Check the browser console for more details.
            </AlertDescription>
          </Alert>
        )}

        <div
          ref={chatContainerRef}
          className="flex-grow overflow-y-auto px-4 py-6 space-y-4"
        >
          {messages.map((msg) => (
            <ChatMessage key={msg.id} message={msg} />
          ))}
        </div>

        <ChatInput
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
          disabled={apiStatus === false}
        />
      </main>

      {showAlternatives && (
        <AlternativeOptions onClose={() => setShowAlternatives(false)} />
      )}


    </div>
  );
};

export default Index;
