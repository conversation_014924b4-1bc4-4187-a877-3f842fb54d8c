
import React from 'react';
import { cn } from '@/lib/utils';
import { User, Bot, Loader2 } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  isLoading?: boolean;
}

interface ChatMessageProps {
  message: Message;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  const { unfilteredMode } = useSettings();

  return (
    <div
      className={cn(
        "flex items-start gap-3 p-4 animate-slide-in",
        isUser ? "flex-row-reverse" : ""
      )}
    >
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser
          ? unfilteredMode ? "bg-red-500" : "bg-xsh-purple-primary"
          : unfilteredMode ? "bg-red-900" : "bg-xsh-light"
      )}>
        {isUser ? (
          <User size={16} className="text-white" />
        ) : (
          <Bot size={16} className={unfilteredMode ? "text-red-400" : "text-xsh-purple-primary"} />
        )}
      </div>

      <div className={cn(
        "rounded-2xl border p-4 max-w-[80%] break-words",
        isUser
          ? "message-user rounded-tr-none"
          : unfilteredMode
            ? "bg-red-950/20 border-red-900/30 rounded-tl-none"
            : "message-bot rounded-tl-none"
      )}>
        {message.isLoading ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <p className="text-sm md:text-base text-muted-foreground">Generating response...</p>
          </div>
        ) : (
          <>
            <p className="text-sm md:text-base whitespace-pre-wrap">{message.content}</p>
            <span className="text-xs text-muted-foreground block mt-2">
              {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              {!isUser && unfilteredMode && message.content && (
                <span className="ml-2 text-red-400">[Unfiltered]</span>
              )}
            </span>
          </>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
