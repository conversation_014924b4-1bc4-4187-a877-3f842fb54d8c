
import React from 'react';
import { MessageCircle } from 'lucide-react';
import SettingsToggle from './SettingsToggle';
import { useSettings } from '@/contexts/SettingsContext';

interface HeaderProps {
  onShowAlternatives?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onShowAlternatives }) => {
  const { unfilteredMode } = useSettings();

  return (
    <header className={`w-full border-b border-white/5 backdrop-blur-lg ${unfilteredMode ? 'bg-red-950/70' : 'bg-xsh-darker/70'}`}>
      <div className="container mx-auto py-4 px-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`flex items-center justify-center h-10 w-10 rounded-md ${unfilteredMode ? 'bg-red-500' : 'bg-xsh-purple-primary'}`}>
            <MessageCircle className="text-white" size={20} />
          </div>
          <div>
            <h1 className="text-xl font-bold tracking-tight">
              <span className="text-white">XSH</span>
              <span className={unfilteredMode ? 'text-red-500' : 'text-xsh-purple-primary'}> CHAT</span>
              {unfilteredMode && <span className="ml-2 text-xs font-normal text-red-400">[UNFILTERED MODE ACTIVE]</span>}
            </h1>
            <p className="text-xs text-muted-foreground">
              {unfilteredMode ? 'No Content Restrictions' : 'Intelligent Conversations'}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="hidden md:flex items-center">
            <div className={`pulse w-2 h-2 ${unfilteredMode ? 'bg-red-500' : 'bg-green-500'} rounded-full relative`}>
              <span className={`absolute inset-0 rounded-full ${unfilteredMode ? 'bg-red-500' : 'bg-green-500'} animate-ping opacity-75`}></span>
            </div>
            <span className="ml-2 text-sm text-muted-foreground">
              {unfilteredMode ? 'Unfiltered' : 'Online'}
            </span>
          </div>

          <SettingsToggle onShowAlternatives={onShowAlternatives} />
        </div>
      </div>
    </header>
  );
};

export default Header;
