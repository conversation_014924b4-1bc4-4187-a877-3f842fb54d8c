
import React, { useState } from 'react';
import { SendH<PERSON>zontal, Loader2 } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, isLoading = false, disabled = false }) => {
  const [message, setMessage] = useState('');
  const { unfilteredMode } = useSettings();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message);
      setMessage('');
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`w-full border-t border-white/5 backdrop-blur-lg p-4 ${unfilteredMode ? 'bg-red-950/70' : 'bg-xsh-darker/70'}`}
    >
      {unfilteredMode && (
        <div className="mb-2 px-2 py-1 bg-red-500/10 border border-red-500/20 rounded text-xs text-red-400 flex items-center">
          <span className="mr-1">🔥</span> UNFILTERED MODE ACTIVE - No restrictions, no limits, no censorship.
        </div>
      )}

      <div className="relative flex items-center w-full">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={
            disabled ? "API connection failed. Check console for details." :
            unfilteredMode ? "Ask anything - no limits, no restrictions..." :
            "Type a message..."
          }
          disabled={isLoading || disabled}
          className={`w-full ${
            disabled ? 'bg-gray-900 border-gray-800 text-gray-500' :
            unfilteredMode ? 'bg-red-950/30 border-red-900/30' :
            'bg-xsh-light border-white/5'
          } border rounded-full py-3 px-5 pr-14 focus:outline-none focus:ring-1 ${
            disabled ? 'focus:ring-gray-700 focus:border-gray-700' :
            unfilteredMode ? 'focus:ring-red-500 focus:border-red-500' :
            'focus:ring-xsh-purple-primary focus:border-xsh-purple-primary'
          } transition-all`}
        />
        <button
          type="submit"
          aria-label="Send message"
          disabled={!message.trim() || isLoading || disabled}
          className={`absolute right-2 p-2 rounded-full ${
            disabled ? 'bg-gray-700' :
            unfilteredMode ? 'bg-red-500' :
            'bg-xsh-purple-primary'
          } text-white disabled:opacity-50 disabled:cursor-not-allowed transition-opacity hover:opacity-90`}
        >
          {isLoading ? (
            <Loader2 size={18} className="animate-spin" />
          ) : (
            <SendHorizontal size={18} />
          )}
        </button>
      </div>
    </form>
  );
};

export default ChatInput;
