import React, { useState } from 'react';
import { Settings, AlertTriangle, ExternalLink } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useSettings } from '@/contexts/SettingsContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface SettingsToggleProps {
  onShowAlternatives?: () => void;
}

const SettingsToggle: React.FC<SettingsToggleProps> = ({ onShowAlternatives }) => {
  const { unfilteredMode, toggleUnfilteredMode } = useSettings();
  const [showWarning, setShowWarning] = useState(false);

  const handleToggle = () => {
    if (!unfilteredMode) {
      // If turning on unfiltered mode, show warning first
      setShowWarning(true);
    } else {
      // If turning off unfiltered mode, just toggle
      toggleUnfilteredMode();
    }
  };

  const confirmUnfilteredMode = () => {
    toggleUnfilteredMode();
    setShowWarning(false);
  };

  const cancelUnfilteredMode = () => {
    setShowWarning(false);
  };

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-full ${unfilteredMode ? 'bg-red-500/10 text-red-500' : ''}`}
          >
            <Settings size={20} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-4">
            <h4 className="font-medium leading-none">Chat Settings</h4>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <div className="font-medium">Unfiltered Mode</div>
                <div className="text-xs text-muted-foreground">
                  Disable content filtering and safety measures
                </div>
              </div>
              <Switch
                checked={unfilteredMode}
                onCheckedChange={handleToggle}
                className={unfilteredMode ? 'bg-red-500' : ''}
              />
            </div>

            {unfilteredMode && (
              <div className="text-xs text-red-500 font-medium flex items-center gap-1">
                <AlertTriangle size={12} />
                <span>Unfiltered mode is active</span>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {showWarning && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg max-w-md w-full p-6 space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Unfiltered mode disables content safety filters. The AI may generate harmful, offensive, or inappropriate content.
              </AlertDescription>
            </Alert>

            <p className="text-sm">
              By enabling unfiltered mode, you acknowledge that:
            </p>

            <ul className="text-sm list-disc pl-5 space-y-1">
              <li>Content may be offensive or inappropriate</li>
              <li>You are responsible for the content you request</li>
              <li>This mode should be used responsibly</li>
            </ul>

            <Alert className="bg-amber-950/20 border-amber-500/30 text-amber-200">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              <AlertDescription className="text-xs">
                Note: Google's Gemini API has built-in ethical guardrails that are difficult to bypass completely.
                If you need truly unfiltered responses, consider alternative solutions.
                {onShowAlternatives && (
                  <Button
                    variant="link"
                    className="text-amber-400 p-0 h-auto text-xs"
                    onClick={() => {
                      cancelUnfilteredMode();
                      onShowAlternatives();
                    }}
                  >
                    View Alternatives <ExternalLink size={10} className="ml-1" />
                  </Button>
                )}
              </AlertDescription>
            </Alert>

            <div className="flex justify-end gap-2 pt-2">
              <Button variant="outline" onClick={cancelUnfilteredMode}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={confirmUnfilteredMode}>
                Enable Unfiltered Mode
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SettingsToggle;
