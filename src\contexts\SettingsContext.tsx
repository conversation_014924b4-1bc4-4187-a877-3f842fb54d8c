import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface SettingsContextType {
  unfilteredMode: boolean;
  toggleUnfilteredMode: () => void;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

interface SettingsProviderProps {
  children: ReactNode;
}

// Local storage key
const UNFILTERED_MODE_STORAGE_KEY = 'xsh-chat-unfiltered-mode';

export function SettingsProvider({ children }: SettingsProviderProps) {
  // Load settings from localStorage
  const [unfilteredMode, setUnfilteredMode] = useState(() => {
    const saved = localStorage.getItem(UNFILTERED_MODE_STORAGE_KEY);
    return saved ? JSON.parse(saved) : false;
  });

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem(UNFILTERED_MODE_STORAGE_KEY, JSON.stringify(unfilteredMode));
  }, [unfilteredMode]);

  const toggleUnfilteredMode = () => {
    setUnfilteredMode(prev => !prev);
  };

  const value = {
    unfilteredMode,
    toggleUnfilteredMode,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}
