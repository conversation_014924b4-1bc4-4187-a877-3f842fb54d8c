
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;

    --primary: 252 82% 74%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 260 54% 45%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 252 82% 74%;

    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-xsh-dark text-foreground font-sans antialiased overflow-x-hidden;
    background-image: radial-gradient(circle at top right, rgba(155, 135, 245, 0.05), transparent 40%), 
                     radial-gradient(circle at bottom left, rgba(155, 135, 245, 0.05), transparent 40%);
  }

  ::-webkit-scrollbar {
    @apply w-1.5;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-xsh-purple-tertiary/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-xsh-purple-tertiary/50;
  }
}

@layer utilities {
  .glass {
    @apply backdrop-blur-md bg-xsh-light/80 border border-white/5 shadow-lg;
  }
  
  .chat-gradient {
    background: linear-gradient(180deg, rgba(155, 135, 245, 0.05) 0%, rgba(110, 89, 165, 0.1) 100%);
  }

  .message-user {
    @apply bg-xsh-purple-primary/10 border-xsh-purple-primary/20;
  }

  .message-bot {
    @apply bg-xsh-light border-white/5;
  }
}
