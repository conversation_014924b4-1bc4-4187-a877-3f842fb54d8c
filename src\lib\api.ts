import { toast } from "@/components/ui/sonner";

// API key for Google's Generative AI
// In a production environment, this would be stored in an environment variable
// For example: const API_KEY = process.env.REACT_APP_GOOGLE_AI_API_KEY;
const API_KEY = "E60d8i8Z8UKRgkXJOyI2NuXAviyz13f0Um4n4bVgpJ";

// Base URL for Google's Generative AI API
const BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models";

// Define multiple models to try in order of preference
const MODELS = [
  "gemini-pro",        // Most reliable model
  "gemini-1.5-flash",  // Alternative model
  "gemini-1.0-pro",    // Older model
  "gemini-2.0-flash"   // Newest model
];

// Alternative API providers that might offer less restricted models
export const ALTERNATIVE_PROVIDERS = [
  {
    name: "OpenAI API",
    description: "Offers models like GPT-4 and GPT-3.5 Turbo with configurable moderation",
    url: "https://platform.openai.com/docs/api-reference",
    setup: "Requires API key from OpenAI platform"
  },
  {
    name: "Anthropic Claude API",
    description: "Claude models with configurable system prompts",
    url: "https://docs.anthropic.com/claude/reference/getting-started-with-the-api",
    setup: "Requires API key from Anthropic"
  },
  {
    name: "Mistral AI",
    description: "Offers various models with different capabilities",
    url: "https://docs.mistral.ai/",
    setup: "Requires API key from Mistral AI platform"
  },
  {
    name: "Ollama",
    description: "Run open-source models locally with no content filtering",
    url: "https://ollama.ai/",
    setup: "Free, runs locally on your machine"
  },
  {
    name: "LocalAI",
    description: "Self-hosted alternative to OpenAI API that runs locally",
    url: "https://github.com/go-skynet/LocalAI",
    setup: "Free, open-source, runs locally"
  }
];

// Interface for chat message
export interface ChatRequestMessage {
  role: "user" | "model";
  parts: { text: string }[];
}

// Interface for API request
interface ApiRequest {
  contents: ChatRequestMessage[];
  generationConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
  };
  safetySettings?: {
    category: string;
    threshold: string;
  }[];
  systemInstruction?: {
    parts: {
      text: string;
    }[];
  };
}

// Interface for API response
interface ApiResponse {
  candidates?: {
    content: {
      parts: { text: string }[];
      role: string;
    };
    finishReason: string;
    safetyRatings?: {
      category: string;
      probability: string;
    }[];
  }[];
  promptFeedback?: {
    blockReason?: string;
    safetyRatings?: {
      category: string;
      probability: string;
    }[];
  };
  error?: {
    code: number;
    message: string;
    status: string;
  };
}

/**
 * Test the API connection with a simple request
 * @returns A boolean indicating if the API is working, and the working model name
 */
export async function testApiConnection(): Promise<{success: boolean, model?: string}> {
  // Create a request body with minimal settings for testing
  const requestBody = {
    contents: [
      {
        parts: [
          {
            text: "Explain how AI works in a few words"
          }
        ]
      }
    ],
    generationConfig: {
      maxOutputTokens: 50,
    }
  };

  console.log("Test request body:", JSON.stringify(requestBody, null, 2));

  // Try each model in order until one works
  for (const model of MODELS) {
    try {
      console.log(`Testing API connection with model: ${model}...`);

      // Make the API request
      const response = await fetch(
        `${BASE_URL}/${model}:generateContent?key=${API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      console.log(`API test status for ${model}:`, response.status);

      // Parse the response
      const data = await response.json();
      console.log(`API test response for ${model}:`, data);

      // Check if there's an API error
      if (data.error) {
        // If the model is overloaded (503), try the next model
        if (data.error.code === 503) {
          console.log(`Model ${model} is overloaded, trying next model...`);
          continue;
        }

        console.error(`API test failed for ${model}:`, data.error);
        continue;
      }

      // Check if we have a valid response
      if (data.candidates && data.candidates.length > 0) {
        console.log(`API test successful with model ${model}:`,
          data.candidates[0].content.parts[0].text);
        return { success: true, model };
      }
    } catch (error) {
      console.error(`API test error with model ${model}:`, error);
      // Continue to the next model
    }
  }

  // If we've tried all models and none worked
  console.error("All models failed the API test");
  return { success: false };
}

/**
 * Send a message to the AI and get a response
 * @param messages Previous conversation history
 * @param unfiltered Whether to use unfiltered mode
 * @param preferredModel Optional preferred model to use
 * @returns The AI's response text
 */
export async function sendMessage(
  messages: ChatRequestMessage[],
  unfiltered: boolean = false,
  preferredModel?: string
): Promise<string> {
  // Convert our message format to Google's format
  const formattedContents = messages.map(msg => ({
    parts: [{ text: msg.parts[0].text }]
  }));

  // Prepare the request body in Google's format
  const requestBody = {
    contents: formattedContents,
    // Optional generation config - use higher temperature for unfiltered mode
    generationConfig: {
      temperature: unfiltered ? 1.0 : 0.7,
      topP: unfiltered ? 0.95 : 0.8,
      topK: 40,
      maxOutputTokens: 800,
    }
  };

  // If unfiltered mode is enabled, add safety settings to disable content filtering
  if (unfiltered) {
    // Set safety settings to minimum
    requestBody.safetySettings = [
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" }
    ];

    // Set temperature higher for more creative responses
    if (requestBody.generationConfig) {
      requestBody.generationConfig.temperature = 1.0;
    }
  }

  // Log the full request for debugging
  console.log("Request body:", JSON.stringify(requestBody, null, 2));

  // Determine which models to try
  let modelsToTry = [...MODELS]; // Create a copy of the models array

  // If a preferred model is specified, try it first
  if (preferredModel) {
    modelsToTry = [preferredModel, ...modelsToTry.filter(m => m !== preferredModel)];
  }

  // Try each model in sequence until one works
  for (const model of modelsToTry) {
    try {
      console.log(`Trying model: ${model}...`);

      // Make the API request
      const response = await fetch(
        `${BASE_URL}/${model}:generateContent?key=${API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      // Log the response status
      console.log(`API response status for ${model}:`, response.status);

      // Parse the response
      const data: ApiResponse = await response.json();

      // Log the response data for debugging
      console.log(`API response data for ${model}:`, JSON.stringify(data, null, 2));

      // Check if there's an API error
      if (data.error) {
        console.error(`API Error with ${model}:`, data.error);

        // If the model is overloaded (503), try the next model
        if (data.error.code === 503) {
          console.log(`Model ${model} is overloaded, trying next model...`);
          continue;
        }

        if (data.error.code === 400) {
          return `There was an error with your request (400): ${data.error.message}`;
        } else if (data.error.code === 403) {
          return `API key invalid or unauthorized (403): ${data.error.message}`;
        } else {
          // For other errors, try the next model
          continue;
        }
      }

      // Check if the response was blocked
      if (data.promptFeedback?.blockReason) {
        if (unfiltered) {
          // If in unfiltered mode and content was blocked, try the next model
          console.log(`Content blocked with ${model} despite unfiltered mode, trying next model...`);
          continue;
        } else {
          return `Your request was blocked by content filters. Reason: ${data.promptFeedback.blockReason}. Try enabling unfiltered mode in settings.`;
        }
      }

      // Check if we have a valid response
      if (!data.candidates || data.candidates.length === 0) {
        // If no candidates, try the next model
        console.log(`No response from ${model}, trying next model...`);
        continue;
      }

      // If we got here, we have a successful response
      console.log(`Successful response from model ${model}`);

      // Return the text response
      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error(`Error calling ${model} API:`, error);
      // Continue to the next model
    }
  }

  // If we've tried all models and none worked
  toast.error("All models failed to respond. Please try again later.");
  return "All available AI models are currently overloaded or experiencing issues. Please try again in a few minutes.";
}
